import { apiClient } from '@/shared/api';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  ResendOtpRequest,
  ResendOtpResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  VerifyForgotPasswordRequest,
  VerifyForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  RefreshTokenResponse,
  GoogleAuthRequest,
  FacebookAuthRequest,
  ZaloAuthRequest,
  VerifyTwoFactorRequest,
  VerifyTwoFactorResponse,
  SelectTwoFactorMethodRequest,
  SelectTwoFactorMethodResponse,
} from '../types/auth.types';

/**
 * Service cho các chức năng xác thực
 */
export const AuthService = {
  /**
   * Đăng nhập
   * @param data Dữ liệu đăng nhập
   * @returns Promise với kết quả đăng nhập
   */
  login: (data: LoginRequest) => {
    return apiClient.post<LoginResponse>('/auth/login', data);
  },

  /**
   * Đăng ký
   * @param data Dữ liệu đăng ký
   * @returns Promise với kết quả đăng ký
   */
  register: (data: RegisterRequest) => {
    return apiClient.post<RegisterResponse>('/auth/register', data);
  },

  /**
   * Xác thực OTP
   * @param data Dữ liệu xác thực OTP
   * @returns Promise với kết quả xác thực OTP
   */
  verifyOtp: (data: VerifyOtpRequest) => {
    return apiClient.post<VerifyOtpResponse>('/auth/verify-otp', data);
  },

  /**
   * Gửi lại OTP
   * @param data Dữ liệu gửi lại OTP
   * @returns Promise với kết quả gửi lại OTP
   */
  resendOtp: (data: ResendOtpRequest) => {
    return apiClient.post<ResendOtpResponse>('/auth/resend-otp', data);
  },

  /**
   * Quên mật khẩu
   * @param data Dữ liệu quên mật khẩu
   * @returns Promise với kết quả quên mật khẩu
   */
  forgotPassword: (data: ForgotPasswordRequest) => {
    return apiClient.post<ForgotPasswordResponse>('/auth/forgot-password', data);
  },

  /**
   * Xác thực quên mật khẩu
   * @param data Dữ liệu xác thực quên mật khẩu
   * @returns Promise với kết quả xác thực quên mật khẩu
   */
  verifyForgotPassword: (data: VerifyForgotPasswordRequest) => {
    return apiClient.post<VerifyForgotPasswordResponse>('/auth/verify-forgot-password', data);
  },

  /**
   * Đặt lại mật khẩu
   * @param data Dữ liệu đặt lại mật khẩu
   * @returns Promise với kết quả đặt lại mật khẩu
   */
  resetPassword: (data: ResetPasswordRequest) => {
    return apiClient.post<ResetPasswordResponse>('/auth/reset-password', data);
  },

  /**
   * Làm mới token
   * @param refreshToken Refresh token
   * @returns Promise với kết quả làm mới token
   */
  refreshToken: (refreshToken: string) => {
    return apiClient.post<RefreshTokenResponse>('/auth/refresh-token', { refreshToken });
  },

  /**
   * Đăng xuất
   * @returns Promise với kết quả đăng xuất
   */
  logout: () => {
    return apiClient.post<null>('/auth/logout');
  },

  /**
   * Lấy URL xác thực Google
   * @param redirectUri URI chuyển hướng sau khi xác thực
   * @returns Promise với URL xác thực Google
   */
  getGoogleAuthUrl: (redirectUri?: string) => {
    return apiClient.get<{ url: string }>(
      `/v1/auth/google/auth-url${redirectUri ? `?redirectUri=${encodeURIComponent(redirectUri)}` : ''}`
    );
  },

  /**
   * Đăng nhập bằng Google
   * @param data Dữ liệu đăng nhập Google
   * @returns Promise với kết quả đăng nhập
   */
  loginWithGoogle: (data: GoogleAuthRequest) => {
    return apiClient.post<LoginResponse>('/auth/google/login', data);
  },

  /**
   * Lấy URL xác thực Facebook
   * @param redirectUri URI chuyển hướng sau khi xác thực
   * @returns Promise với URL xác thực Facebook
   */
  getFacebookAuthUrl: (redirectUri?: string) => {
    return apiClient.get<{ url: string }>(
      `/auth/facebook/auth-url${redirectUri ? `?redirectUri=${encodeURIComponent(redirectUri)}` : ''}`
    );
  },

  /**
   * Lấy URL xác thực Zalo
   * @param redirectUri URI chuyển hướng sau khi xác thực
   * @returns Promise với URL xác thực Zalo
   */
  getZaloAuthUrl: (redirectUri?: string) => {
    return apiClient.get<{ url: string }>(
      `/auth/zalo/auth-url${redirectUri ? `?redirectUri=${encodeURIComponent(redirectUri)}` : ''}`
    );
  },

  /**
   * Đăng nhập bằng Facebook
   * @param data Dữ liệu đăng nhập Facebook
   * @returns Promise với kết quả đăng nhập
   */
  loginWithFacebook: (data: FacebookAuthRequest) => {
    return apiClient.post<LoginResponse>('/auth/facebook/login', data);
  },

  /**
   * Đăng nhập bằng Zalo
   * @param data Dữ liệu đăng nhập Zalo
   * @returns Promise với kết quả đăng nhập
   */
  loginWithZalo: (data: ZaloAuthRequest) => {
    return apiClient.post<LoginResponse>('/auth/zalo/login', data);
  },

  /**
   * Lấy thông tin người dùng hiện tại
   * @returns Promise với thông tin người dùng
   */
  getCurrentUser: () => {
    return apiClient.get<LoginResponse>('/auth/me');
  },

  /**
   * Xác thực hai lớp (2FA)
   * @param data Dữ liệu xác thực hai lớp
   * @returns Promise với kết quả xác thực hai lớp
   */
  verifyTwoFactor: (data: VerifyTwoFactorRequest) => {
    return apiClient.post<VerifyTwoFactorResponse>('/auth/verify-2fa', {
      token: data.verifyToken,
      otp: data.code,
      platform: data.method,
    });
  },

  /**
   * Chọn phương thức xác thực hai lớp
   * @param data Dữ liệu chọn phương thức xác thực hai lớp
   * @returns Promise với kết quả chọn phương thức
   */
  selectTwoFactorMethod: (data: SelectTwoFactorMethodRequest) => {
    return apiClient.post<SelectTwoFactorMethodResponse>('/auth/select-2fa-method', data);
  },
};
